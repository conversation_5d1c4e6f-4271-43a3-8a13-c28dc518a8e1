# Quantum-Proof Encryption (Rust Implementation)

<div align="center">

![Version](https://img.shields.io/badge/version-4.0.0-blue.svg)
![Rust](https://img.shields.io/badge/rust-1.70%2B-orange.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

A high-performance, streaming-capable implementation of post-quantum encryption using CRYSTALS-Kyber and AES-256-GCM, featuring a beautiful Terminal UI.

[Features](#features) • [Installation](#installation) • [Usage](#usage) • [UI Mode](#terminal-ui) • [Examples](#examples) • [Security](#security)

</div>

## ✨ Features

- **🔐 Post-Quantum Security**: Uses CRYSTALS-Kyber (ML-KEM) for quantum-resistant key encapsulation
- **🚀 Streaming Architecture**: Efficiently handles large files without loading them into memory
- **🎨 Beautiful Terminal UI**: Interactive interface for easy encryption/decryption
- **🔒 Multiple Security Levels**: 
  - Standard (Kyber512, NIST Level 1)
  - High (Kyber768, NIST Level 3) - Default
  - Maximum (Kyber1024, NIST Level 5)
- **🔑 Flexible Encryption**: Support for both public-key and password-based encryption
- **📁 Comprehensive File Support**: Encrypt individual files, folders, and text
- **📊 Progress Indicators**: Visual feedback for long operations
- **🌍 Cross-Platform**: Works on Windows, macOS, and Linux
- **⚡ High Performance**: Optimized Rust implementation with hardware acceleration
- **🔄 Version Compatibility**: Backward compatible with previous file format versions
- **🧹 Automatic Cleanup**: Smart file management with automatic cleanup of temporary files
- **📈 Large File Support**: Handles files up to 25GB with streaming and chunked processing

## 🆕 Recent Improvements (v4.0.1)

- **✅ Fixed Version Compatibility**: Resolved "Invalid format version" errors for better backward compatibility
- **🚀 Enhanced Large File Support**: Improved handling of files up to 25GB with better memory management
- **📊 Fixed Progress Indicators**: Resolved issues with upload/download progress displays
- **🧹 Improved File Cleanup**: Enhanced automatic cleanup of temporary and downloaded files
- **🔧 Better Error Handling**: More robust error handling and recovery mechanisms

## 🎯 Quick Start

The fastest way to get started is with the Terminal UI:

```bash
# Install
cargo install --path .

# Launch the beautiful Terminal UI
qpe --ui
```

<details>
<summary>📸 UI Preview (click to expand)</summary>

```
╔═══════════════════════════════════════════════════════════════╗
║          Ultimate Quantum-Proof Encryption Program            ║
║              Version 4.0 - Streaming & Hardened               ║
╚═══════════════════════════════════════════════════════════════╝

┌─────────────────────Main Menu─────────────────────┐
│                                                   │
│  🔑 Generate Keys                                │
│     Create a new quantum-safe keypair             │
│                                                   │
│  🔒 Encrypt File                                 │
│     Encrypt a single file                         │
│                                                   │
│  🔓 Decrypt File                                 │
│     Decrypt an encrypted file                     │
│                                                   │
└───────────────────────────────────────────────────┘

🛡️  Security: HIGH        Main Menu        📚 [F1] Help | [Q] Quit
```

</details>

## 📦 Installation

### Prerequisites

- Rust 1.70 or later
- Cargo (comes with Rust)

### Building from Source

```bash
# Clone the repository
git clone <your-repo-url>
cd quantum-proof-encryption

# Build in release mode (recommended)
make release
# OR
cargo build --release

# Install globally
make install
# OR
cargo install --path .
```

### Quick Build Scripts

```bash
# Linux/macOS
chmod +x build.sh
./build.sh

# Windows
build.bat
```

## 🖥️ Terminal UI

The Terminal UI provides an intuitive interface for all encryption operations:

### Navigation
- **↑/↓** - Navigate menu items
- **Enter** - Select item
- **Tab** - Switch between input fields
- **Esc** - Go back / Cancel
- **Q** - Quit application
- **S** - Cycle security levels (in main menu)
- **F1** - Show help

### Features
- Interactive file/folder selection
- Real-time progress bars
- Password input with masking
- Inline help and status messages
- Beautiful color-coded interface

## 📖 Usage

### Command Line Interface

#### Generate a Keypair

```bash
# Basic keypair generation
qpe generate-keys

# With custom output paths and encrypted private key
qpe generate-keys --pub-out alice.pub --priv-out alice.key --encrypt-private-key

# Maximum security level
qpe --security maximum generate-keys
```

#### Encrypt Files

```bash
# Encrypt with public key
qpe encrypt-file document.pdf --public-key alice.pub

# Encrypt with password
qpe encrypt-file document.pdf --password

# Encrypt multiple files
qpe encrypt-file *.txt --public-key alice.pub --output-dir encrypted/

# Custom output name
qpe encrypt-file video.mp4 --public-key alice.pub --output secure_video.qpe
```

#### Decrypt Files

```bash
# Decrypt with private key
qpe decrypt-file document.pdf.qpe --private-key alice.key

# Decrypt with password-protected private key
qpe decrypt-file document.pdf.qpe --private-key alice.key --key-password

# Decrypt multiple files
qpe decrypt-file *.qpe --private-key alice.key --output-dir decrypted/
```

#### Encrypt/Decrypt Folders

```bash
# Encrypt folder to single archive
qpe encrypt-folder my_folder --public-key alice.pub

# With custom compression (0-9)
qpe encrypt-folder my_folder --public-key alice.pub --compression 9

# Decrypt folder archive
qpe decrypt-folder my_folder.qpe --private-key alice.key
```

#### Text Encryption

```bash
# Encrypt text message
qpe encrypt-text "Secret message" --public-key alice.pub

# Decrypt text
qpe decrypt-text "BASE64_ENCRYPTED_TEXT" --private-key alice.key
```

## 🎓 Examples

### Basic Workflow

```bash
# 1. Alice generates her keypair
qpe generate-keys --pub-out alice.pub --priv-out alice.key --encrypt-private-key

# 2. Alice shares alice.pub with Bob

# 3. Bob encrypts a file for Alice
qpe encrypt-file contract.pdf --public-key alice.pub

# 4. Alice decrypts the file
qpe decrypt-file contract.pdf.qpe --private-key alice.key --key-password
```

### Secure Communication

```bash
# Encrypt a message
MESSAGE=$(qpe encrypt-text "Meet at the usual place at 3pm" --public-key recipient.pub)

# Send $MESSAGE through any channel (email, chat, etc.)

# Recipient decrypts
qpe decrypt-text "$MESSAGE" --private-key my.key
```

### Backup Encryption

```bash
# Encrypt backup with password
qpe encrypt-folder ~/Documents --password --output backup_2024.qpe

# Restore later
qpe decrypt-folder backup_2024.qpe --password --output ~/Documents_Restored
```

## 🔒 Security

### Security Levels

| Level | Kyber Variant | NIST Level | Argon2 Parameters | Use Case |
|-------|---------------|------------|-------------------|----------|
| **standard** | Kyber512 | 1 | t=4, m=64MB | General use, good performance |
| **high** | Kyber768 | 3 | t=6, m=128MB | Recommended default |
| **maximum** | Kyber1024 | 5 | t=8, m=256MB | Maximum security, critical data |

### Cryptographic Details

- **Key Encapsulation**: CRYSTALS-Kyber (winner of NIST PQC competition)
- **Symmetric Encryption**: AES-256-GCM (authenticated encryption)
- **Key Derivation**: Argon2id (memory-hard, resistant to GPU/ASIC attacks)
- **Hash Functions**: SHA3-256 (for key derivation from Kyber shared secrets)
- **Random Generation**: Cryptographically secure via `rand` crate

## ⚡ Performance

The Rust implementation provides several performance advantages:

- **Memory Efficiency**: Files are processed in 1MB chunks
- **Zero-Copy Operations**: Minimal memory allocations
- **Hardware Acceleration**: Uses CPU instructions for AES when available
- **Parallel Processing**: Multi-core support for Argon2 operations

### Benchmarks

Run benchmarks with:
```bash
make bench
# OR
cargo bench
```

## 🧪 Testing

```bash
# Run all tests
make test

# Run comprehensive test suite
chmod +x test.sh
./test.sh

# Run specific test
cargo test test_file_encryption
```

## 🛠️ Development

### Project Structure

```
quantum-proof-encryption/
├── src/
│   ├── main.rs           # CLI entry point
│   ├── lib.rs            # Library root
│   ├── errors.rs         # Error types
│   ├── models.rs         # Data structures
│   ├── utils.rs          # Utilities
│   ├── key_management.rs # Key operations
│   ├── streaming.rs      # Core encryption
│   ├── encryption.rs     # High-level API
│   └── ui.rs            # Terminal UI
├── examples/
│   └── demo.rs          # Demo application
├── tests/               # Integration tests
├── benches/             # Benchmarks
└── Cargo.toml          # Dependencies
```

### Building Documentation

```bash
make doc
# OR
cargo doc --open
```

## 🤝 Contributing

Contributions are welcome! Please ensure:

1. Code follows Rust conventions (`cargo fmt` and `cargo clippy`)
2. All tests pass (`cargo test`)
3. New features include tests
4. Documentation is updated

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Security Notice

While this implementation uses well-reviewed cryptographic libraries:
- `pqc_kyber`: Pure Rust implementation of CRYSTALS-Kyber
- `aes-gcm`: Hardware-accelerated AES-GCM from RustCrypto
- `argon2`: Memory-hard key derivation function

**This code has not been independently audited. Use in production at your own risk.**

## 🆘 Troubleshooting

<details>
<summary>Common Issues and Solutions</summary>

### "Command not found"
```bash
# Ensure it's installed
cargo install --path .
# Or use cargo run
cargo run -- --ui
```

### "Key integrity check failed"
- The key file may be corrupted
- Ensure you're using the correct key file
- Check file permissions

### "Failed to decrypt: Wrong password?"
- Passwords are case-sensitive
- Check caps lock
- Try regenerating the keypair if password is lost

### UI Display Issues
- Ensure terminal is at least 80x24 characters
- Try different terminal emulator
- Use `--no-ui` flag for CLI mode

### Performance Issues
- Use release build: `cargo build --release`
- Check available disk space
- Consider lower security level for better performance

</details>

## 📚 Resources

- [CRYSTALS-Kyber Specification](https://pq-crystals.org/kyber/)
- [NIST Post-Quantum Cryptography](https://csrc.nist.gov/projects/post-quantum-cryptography)
- [AES-GCM Mode](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-38d.pdf)
- [Argon2 Specification](https://github.com/P-H-C/phc-winner-argon2)

## 🎉 Acknowledgments

Built with excellent Rust cryptography libraries from the RustCrypto project and the Kyber reference implementation.

## Features

- **Post-Quantum Security**: Uses CRYSTALS-Kyber (ML-KEM) for quantum-resistant key encapsulation
- **Streaming Architecture**: Efficiently handles large files without loading them into memory
- **Multiple Security Levels**: 
  - Standard (Kyber512, NIST Level 1)
  - High (Kyber768, NIST Level 3) - Default
  - Maximum (Kyber1024, NIST Level 5)
- **Flexible Encryption**: Support for both public-key and password-based encryption
- **Comprehensive File Support**: Encrypt individual files, folders, and text
- **Progress Indicators**: Visual feedback for long operations
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Rust 1.70 or later
- Cargo (comes with Rust)

### Building from Source

```bash
# Clone the repository (or create a new project with the provided files)
git clone <your-repo-url>
cd quantum-proof-encryption

# Build in release mode for optimal performance
cargo build --release

# The binary will be available at target/release/qpe
```

### Installing

```bash
# Install to your local cargo bin directory
cargo install --path .
```

## Usage

### Generate a Keypair

```bash
# Generate keys with default settings
qpe generate-keys

# Generate with password-protected private key
qpe generate-keys --encrypt-private-key

# Generate with maximum security
qpe --security maximum generate-keys --pub-out alice.pub --priv-out alice.key
```

### Encrypt Files

```bash
# Encrypt a single file with public key
qpe encrypt-file document.pdf --public-key alice.pub

# Encrypt with password
qpe encrypt-file document.pdf --password

# Encrypt multiple files to a directory
qpe encrypt-file *.txt --public-key alice.pub --output-dir encrypted/

# Encrypt with custom output name
qpe encrypt-file video.mp4 --public-key alice.pub --output secure_video.qpe
```

### Decrypt Files

```bash
# Decrypt with private key
qpe decrypt-file document.pdf.qpe --private-key alice.key

# Decrypt with password-protected private key
qpe decrypt-file document.pdf.qpe --private-key alice.key --key-password

# Decrypt with password
qpe decrypt-file document.pdf.qpe --password

# Decrypt multiple files
qpe decrypt-file *.qpe --private-key alice.key --output-dir decrypted/
```

### Encrypt/Decrypt Folders

```bash
# Encrypt an entire folder
qpe encrypt-folder my_folder --public-key alice.pub

# Decrypt folder archive
qpe decrypt-folder my_folder.qpe --private-key alice.key

# With custom compression level (0-9)
qpe encrypt-folder my_folder --public-key alice.pub --compression 9
```

### Text Encryption

```bash
# Encrypt text
qpe encrypt-text "Secret message" --public-key alice.pub

# Decrypt text
qpe decrypt-text "BASE64_ENCRYPTED_TEXT" --private-key alice.key
```

### Key Management

```bash
# View key information
qpe key-info alice.pub

# Generate secure password
qpe generate-password --length 32 --readable
```

## Security Levels

| Level | Kyber Variant | NIST Level | Argon2 Parameters | Use Case |
|-------|---------------|------------|-------------------|----------|
| standard | Kyber512 | 1 | t=4, m=64MB | General use, good performance |
| high | Kyber768 | 3 | t=6, m=128MB | Recommended default |
| maximum | Kyber1024 | 5 | t=8, m=256MB | Maximum security |

## Examples

### Basic File Encryption Workflow

```bash
# 1. Generate keypair
qpe generate-keys --pub-out alice.pub --priv-out alice.key --encrypt-private-key

# 2. Encrypt a file
qpe encrypt-file sensitive_data.xlsx --public-key alice.pub

# 3. Decrypt the file
qpe decrypt-file sensitive_data.xlsx.qpe --private-key alice.key --key-password
```

### Batch Processing

```bash
# Encrypt all PDFs in a directory
for file in *.pdf; do
    qpe encrypt-file "$file" --public-key company.pub --output-dir encrypted/
done

# Or use the built-in batch support
qpe encrypt-file *.pdf --public-key company.pub --output-dir encrypted/
```

### Secure Communication

```bash
# Alice generates her keypair
qpe generate-keys --pub-out alice.pub --priv-out alice.key

# Alice shares alice.pub with Bob
# Bob encrypts a message for Alice
qpe encrypt-text "Meet at the usual place at 3pm" --public-key alice.pub

# Alice decrypts Bob's message
qpe decrypt-text "<encrypted_text>" --private-key alice.key
```

## Performance

The Rust implementation provides several performance advantages:

- **Streaming**: Files are processed in 1MB chunks, allowing encryption of files larger than available RAM
- **Zero-copy operations**: Minimal memory allocations during encryption/decryption
- **Parallel processing**: Utilizes multiple CPU cores for Argon2 operations
- **Optimized cryptography**: Uses hardware acceleration when available

## Error Handling

The program provides detailed error messages for common issues:

- File not found
- Invalid keys or passwords
- Corrupted encrypted data
- Insufficient permissions
- Unsupported format versions

## Contributing

Contributions are welcome! Please ensure:

1. Code follows Rust conventions (use `cargo fmt` and `cargo clippy`)
2. All tests pass (`cargo test`)
3. New features include tests
4. Documentation is updated

## License

This project is licensed under the MIT License.

## Security Notice

This implementation uses well-reviewed cryptographic libraries:
- `pqc_kyber`: Pure Rust implementation of CRYSTALS-Kyber
- `aes-gcm`: Hardware-accelerated AES-GCM from RustCrypto
- `argon2`: Memory-hard key derivation function

However, this code has not been independently audited. Use in production at your own risk.

## Troubleshooting

### "Key integrity check failed"
- The key file may be corrupted
- Ensure you're using the correct key file
- Check file permissions

### "Failed to decrypt private key. Wrong password?"
- Double-check the password
- Ensure caps lock is off
- Try regenerating the keypair if the password is lost

### Performance issues with large files
- Use release build (`cargo build --release`)
- Ensure sufficient disk space for temporary files
- Consider using lower security level for better performance

## Changelog

### Version 4.0.0
- Complete Rust rewrite with streaming support
- Improved memory efficiency
- Progress indicators for long operations
- Better error handling and recovery
- Cross-platform compatibility improvements